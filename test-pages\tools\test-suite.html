<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhotoEditorDemo - 测试套件重定向</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .redirect-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            margin: 20px;
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {

            0%,
            20%,
            50%,
            80%,
            100% {
                transform: translateY(0);
            }

            40% {
                transform: translateY(-10px);
            }

            60% {
                transform: translateY(-5px);
            }
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .description {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .redirect-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #ffd700;
        }

        .countdown {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffd700;
            margin: 20px 0;
        }

        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .btn.primary {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            border-color: #ffd700;
        }

        .btn.primary:hover {
            background: linear-gradient(45deg, #ffed4e, #ffd700);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffd700, #ffed4e);
            width: 0%;
            transition: width 0.1s ease;
        }

        @media (max-width: 768px) {
            .redirect-container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>

<body>
    <div class="redirect-container">
        <div class="icon">🧪</div>
        <h1>测试套件重定向</h1>
        <div class="description">
            PhotoEditorDemo项目的测试页面已重新组织到专门的测试目录中。
            您将被自动重定向到新的测试套件界面。
        </div>

        <div class="redirect-info">
            <strong>📁 新位置</strong>: test-pages/index.html<br>
            <strong>🔄 自动重定向</strong>: <span class="countdown" id="countdown">5</span> 秒后自动跳转
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="buttons">
            <a href="test-pages/index.html" class="btn primary">
                🚀 立即访问测试套件
            </a>
            <a href="http://localhost:8081/" class="btn">
                🏠 返回项目主页
            </a>
            <a href="docs/troubleshooting/" class="btn">
                📚 查看文档
            </a>
        </div>
    </div>

    <script>
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        const progressFill = document.getElementById('progressFill');

        function updateCountdown() {
            countdownElement.textContent = countdown;
            progressFill.style.width = ((5 - countdown) / 5 * 100) + '%';

            if (countdown <= 0) {
                window.location.href = 'test-pages/index.html';
                return;
            }

            countdown--;
            setTimeout(updateCountdown, 1000);
        }

        // 开始倒计时
        setTimeout(updateCountdown, 1000);

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function () {
            const container = document.querySelector('.redirect-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                container.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>

</html>